<?php

use App\Http\Controllers\Api\Document\ApiDocumentController;
use App\Http\Controllers\Api\Order\APIOrderV2Controller;
use App\Http\Controllers\Api\Order\OrderController;

use App\Http\Controllers\Api\Order\OrderServiceController;
use App\Http\Controllers\Api\Order\OrderVController;
use App\Http\Controllers\Api\Order\OrderProductionController;
use App\Http\Controllers\CronJob\RechargeCronJobController;
use App\Http\Controllers\CronJob\ServerActionController;
use App\Http\Controllers\CronJob\StatusOrderServiceController;
use App\Http\Controllers\CronJob\TelegramController;

use App\Http\Controllers\Guard\ClientProductController;
use App\Http\Controllers\CronJob\PaymentController;
use App\Http\Controllers\CronJob\TestController;
use App\Http\Controllers\CronJob\PriceController;
use App\Http\Controllers\CronJob\OrderDataController;
use App\Http\Controllers\CronJob\CardController;
use App\Http\Controllers\Tool\ToolController;
use App\Http\Controllers\CronJob\ChildpanelServiceController;
// use App\Http\Controllers\Admin\AutomationController; // Đã xóa để tránh mất dữ liệu

use App\Library\CloudflareController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use function Pest\Laravel\json;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::match(['get', 'post'], 'v2', [APIOrderV2Controller::class, 'APIOrderV2'])->name('api.order.v2');
Route::get('update', [ChildpanelServiceController::class, 'updateSubSiteServices'])->name('cron-job.update');
    
Route::prefix('v1')->group(function () {
    Route::post('start/create/order', [OrderController::class, 'createOrder'])->name('api.create.order')->middleware(['xss']);

    // Order update route - API endpoint để cập nhật đơn hàng
    Route::post('order/update', [OrderController::class, 'updateOrder'])->name('api.order.update')->middleware(['xss']);

    // Order refund route - API endpoint để hoàn tiền đơn hàng
    Route::post('order/refund', [OrderController::class, 'refundOrder'])->name('api.order.refund')->middleware(['xss']);

    // Order warranty route - API endpoint để bảo hành đơn hàng
    Route::post('order/warranty', [OrderController::class, 'warrantyOrder'])->name('api.order.warranty')->middleware(['xss']);

    Route::get('update/price', [ChildpanelServiceController::class, 'updateSubSiteServices'])->name('cron-job.update.price');
        
    Route::get('status/order/2mxh', [StatusOrderServiceController::class, 'cronJobStatusService2mxh'])->name('cron-job.status.2mxh');
    Route::get('status/order', [StatusOrderServiceController::class, 'cronService'])->name('cron-job.status.service');
    Route::get('refund/order', [StatusOrderServiceController::class, 'refundAllOrders'])->name('cron-job.refund.order');
    Route::get('get/services', [ToolController::class, 'getServices'])->name('ndh.get.services');
    Route::get('get/order', [ToolController::class, 'getOrder'])->name('ndh.get.order');
    Route::get('price/smm', [PriceController::class, 'checkPriceService'])->name('cron-job.price.smm');
    Route::post('create/order', [OrderVController::class, 'createOrder'])->name('api.v2.create.order')->middleware('xss');
    Route::prefix('tools')->group(function () {
        Route::get('get-uid', [ToolController::class, 'getUid'])->name('tools.get-uid');
    });

    // Payment routes - API endpoints cho các ngân hàng
    Route::get('payment/{code}', [RechargeCronJobController::class, 'payment'])->name('api.payment');
    
    // Card route - API endpoint cho thẻ cào
    Route::get('card', [CardController::class, 'card'])->name('api.card');
    
    // Update orders status routes - API endpoints cho cập nhật trạng thái đơn hàng
    Route::get('update/orders-status-smm-partners', [StatusOrderServiceController::class, 'updateOrdersStatusSmmPartners'])->name('api.update.orders-status-smm-partners');
    Route::get('update/orders-status-tds', [StatusOrderServiceController::class, 'updateOrdersStatusTds'])->name('api.update.orders-status-tds');

    // Automation cron job đã được vô hiệu hóa để tránh mất dữ liệu quan trọng
    // Route::get('automation/run', [AutomationController::class, 'runAll'])->name('cron-job.automation.run');
    
});

Route::prefix('product')->group(function () {
    Route::post('/buy', [ClientProductController::class, 'BuyProduct'])->name('client.product.buy')->middleware(['xss']);
    Route::get('get-data', [ClientProductController::class, 'CronJobProduct'])->name('api.product.get-data');
    Route::get('get-service-child', [ClientProductController::class, 'CronJobProductChild'])->name('api.product.get-service-child');
    Route::get('categories', [OrderProductionController::class, 'getCategories'])->name('api.product.categories');
    Route::get('/{slug}', [OrderProductionController::class, 'getProductBySlug'])->name('api.product.product-by-slug');
    Route::post('order', [OrderProductionController::class, 'orderProduct'])->name('api.product.order');
});

Route::prefix('lt')->group(function () {
    Route::get('get/me', [ApiDocumentController::class, 'getMe'])->name('api-document.get-me');

    Route::prefix('services')->group(function () {
        Route::get('/', [ApiDocumentController::class, 'getServices'])->name('api-document.get-services');
        Route::get('servers', [ApiDocumentController::class, 'getServersByServices'])->name('api-document.get-servers');
        Route::get('{id}', [ApiDocumentController::class, 'getServiceById'])->name('api-document.get-service');
    });

    Route::prefix('servers')->group(function () {
        Route::get('/', [ApiDocumentController::class, 'getServers'])->name('api-document.get-servers');
        Route::get('{id}', [ApiDocumentController::class, 'getServerById'])->name('api-document.get-server');
    });

    Route::prefix('orders')->group(function () {
        Route::get('/', [ApiDocumentController::class, 'getOrders'])->name('api-document.get-orders');
        Route::get('{id}', [ApiDocumentController::class, 'getOrderById'])->name('api-document.get-order');
    });
});

// Route::get('clf', function () {
//     $start = new CloudflareController();
//     $dataa = $start->addDomain('dichvusnet.com');
//     return $dataa;
// }); 

Route::prefix('telegram')->group(function () {
    Route::get('get-webhook-info', function () {
        $telegram = new App\Library\TelegramSdk();
        $response = $telegram->botNotify()->getWebhookInfo();
        dd($response);
    });

    // remove webhook
    Route::get('remove-webhook', function () {
        $telegram = new App\Library\TelegramSdk();
        $response = $telegram->botNotify()->removeWebhook();
        dd($response);
    });

    // webhook
    Route::any('weere', [TelegramController::class, 'callbackData'])->name('telegram.set-webhook');
});

// Thêm route cho thông báo ticket và doanh thu
Route::get('/v1/notify/ticket-revenue', [App\Http\Controllers\CronJob\NotifyController::class, 'ticketRevenueNotify']);


